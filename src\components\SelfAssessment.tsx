import { AnimatePresence, motion } from "framer-motion";
import { useRef, useState } from "react";
import { FiChevronDown } from "react-icons/fi";

const QUESTIONS = [
  {
    text: "How much of your content planning is automated versus manual?",
    min: "Completely manual",
    max: "Fully automated",
  },
  {
    text: "Do you use AI or scripts to generate or repurpose content?",
    min: "Never",
    max: "For all content",
  },
  {
    text: "How are approvals and revisions handled?",
    min: "All manual",
    max: "Fully automated",
  },
  {
    text: "How integrated is your content workflow across tools and platforms?",
    min: "Separate systems",
    max: "Fully integrated",
  },
  {
    text: "How do you distribute content to channels?",
    min: "Each manually",
    max: "Unified system",
  },
  {
    text: "Adaptability to new clients/platforms?",
    min: (
      <>
        Requires <br /> complete rework
      </>
    ),
    max: (
      <>
        Seamless <br /> integration
      </>
    ),
  },
  {
    text: "How much time do you spend on repetitive content tasks?",
    min: "Most of our time",
    max: "Minimal time",
  },
];

function calcResult(scores: number[]) {
  // Round the average to the nearest 0.1 for a smoother experience
  const avg =
    Math.round((scores.reduce((a, b) => a + b, 0) / scores.length) * 10) / 10;

  if (avg < 1) {
    return {
      title: "Early Exploration Stage",
      advice:
        "You're just beginning your automation journey. Start by identifying one repetitive content task that consumes significant time and explore simple automation tools to address it. Even small improvements can yield noticeable time savings and demonstrate the value of automation to your team.",
    };
  } else if (avg < 2) {
    return {
      title: "Foundation Building Stage",
      advice:
        "You've taken the first steps toward automation. Now is the time to formalize your approach by documenting your content workflows and identifying bottlenecks. Consider implementing template systems and basic scheduling tools to create a foundation for more advanced automation.",
    };
  } else if (avg < 3) {
    return {
      title: "Growing Automation Awareness",
      advice:
        "You're using several automation tools but may lack a cohesive strategy. Focus on connecting your existing tools and standardizing processes across clients. This is an excellent time to explore AI-assisted content generation for first drafts while maintaining your unique voice and quality standards.",
    };
  } else if (avg < 4) {
    return {
      title: "Strategic Implementation Phase",
      advice:
        "You have a solid automation foundation in place. Your next steps should focus on creating a unified ecosystem where your tools work together seamlessly. Consider implementing workflow automation that connects your content planning, creation, approval, and distribution processes.",
    };
  } else if (avg < 5) {
    return {
      title: "Advanced Automation Ecosystem",
      advice:
        "Your agency has embraced automation across most content processes. To reach the next level, explore predictive content planning using performance data, implement dynamic content personalization, and develop custom integrations that perfectly match your unique workflow needs.",
    };
  } else {
    return {
      title: "Automation Excellence",
      advice:
        "You're operating at the cutting edge of content automation. Your focus now should be on continuous refinement, exploring emerging AI technologies, and developing proprietary systems that give you a competitive advantage. Consider how you can use your automation expertise as a selling point to attract larger clients.",
    };
  }
}

// Smooth scroll utility function
const smoothScrollToElement = (element: HTMLElement, offset: number = 100) => {
  const elementRect = element.getBoundingClientRect();
  const absoluteElementTop = elementRect.top + window.pageYOffset;
  const targetPosition = absoluteElementTop - offset;

  const startPosition = window.pageYOffset;
  const distance = targetPosition - startPosition;
  const duration = 800;
  let startTime: number | null = null;

  const easeInOutCubic = (t: number): number => {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  };

  const animation = (currentTime: number) => {
    if (startTime === null) startTime = currentTime;
    const timeElapsed = currentTime - startTime;
    const progress = Math.min(timeElapsed / duration, 1);
    const ease = easeInOutCubic(progress);

    window.scrollTo(0, startPosition + distance * ease);

    if (progress < 1) {
      requestAnimationFrame(animation);
    }
  };

  requestAnimationFrame(animation);
};

const SelfAssessment = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [responses, setResponses] = useState<number[]>(
    Array(QUESTIONS.length).fill(0),
  );
  const [submitted, setSubmitted] = useState(false);
  const resultsRef = useRef<HTMLDivElement>(null);
  const assessmentRef = useRef<HTMLDivElement>(null);

  const handleSlider = (idx: number, val: number) => {
    setResponses((prev) => {
      const next = [...prev];
      next[idx] = val;
      return next;
    });
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSubmitted(true);

    // Smooth scroll to results after animation
    setTimeout(() => {
      if (resultsRef.current) {
        smoothScrollToElement(resultsRef.current, 150);
      }
    }, 600); // Slightly longer delay to let the results animation start
  };

  const handleRetake = () => {
    setSubmitted(false);
    setResponses(Array(QUESTIONS.length).fill(0));

    // Smooth scroll back to the form after state update
    setTimeout(() => {
      if (assessmentRef.current) {
        smoothScrollToElement(assessmentRef.current, 100);
      }
    }, 300); // Wait for form to re-render
  };

  const result = calcResult(responses);

  return (
    <section className="section-padding bg-gray-950 relative">
      {/* Decorative U-shaped Frame */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-full max-w-3xl mx-auto pointer-events-none">
        {/* Top horizontal line */}
        <div className="h-[2px] bg-gradient-to-r from-purple-400 to-pink-600"></div>

        {/* Container for vertical lines */}
        <div className="relative">
          {/* Left vertical line */}
          <div className="absolute left-0 top-0 w-[2px] h-[250px] bg-gradient-to-b from-purple-400 via-purple-400/50 to-transparent"></div>

          {/* Right vertical line */}
          <div className="absolute right-0 top-0 w-[2px] h-[250px] bg-gradient-to-b from-pink-600 via-pink-600/50 to-transparent"></div>
        </div>
      </div>

      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="mx-auto max-w-2xl text-center"
        >
          <h2 className="section-title mb-3">
            Content Automation Self-Assessment
          </h2>
          <p className="text-xl text-gray-300 mb-6">
            Gauge where your agency stands on the automation journey and
            discover your next steps.
          </p>
          <p className="mb-8 text-gray-400">
            Try our quick self-evaluation tool to help identify your current
            automation maturity level and see personalized recommendations.
          </p>

          {/* Dropdown Button */}
          <motion.button
            onClick={() => {
              const newIsOpen = !isOpen;
              setIsOpen(newIsOpen);

              // Smooth scroll to assessment when opening
              if (newIsOpen) {
                setTimeout(() => {
                  if (assessmentRef.current) {
                    smoothScrollToElement(assessmentRef.current, 80);
                  }
                }, 400); // Wait for dropdown animation to start
              }
            }}
            whileHover={{
              scale: 1.02,
              boxShadow: "0 10px 25px rgba(147, 51, 234, 0.3)",
              transition: { duration: 0.15 }
            }}
            whileTap={{ scale: 0.98 }}
            className="w-full bg-purple-600 rounded-md px-6 py-4 text-center text-lg font-semibold text-white shadow-lg transition duration-200 hover:bg-purple-700 flex items-center justify-center gap-2"
            aria-expanded={isOpen}
          >
            <span>Take Self-Assessment</span>
            <motion.div
              animate={{ rotate: isOpen ? 180 : 0 }}
              transition={{ duration: 0.4, ease: [0.4, 0.0, 0.2, 1] }}
            >
              <FiChevronDown className="h-5 w-5" />
            </motion.div>
          </motion.button>

          {/* Dropdown Content */}
          <AnimatePresence mode="wait">
            {isOpen && (
              <motion.div
                key="assessment-content"
                ref={assessmentRef}
                initial={{ height: 0, opacity: 0, y: -20 }}
                animate={{ height: "auto", opacity: 1, y: 0 }}
                exit={{ height: 0, opacity: 0, y: -20 }}
                transition={{
                  duration: 0.5,
                  ease: [0.4, 0.0, 0.2, 1],
                  opacity: { duration: 0.3 }
                }}
                className="overflow-hidden"
              >
                <div className="mt-4 rounded-2xl border border-gray-800 bg-gray-900/70 p-8 text-center backdrop-blur-sm">
                  {!submitted ? (
                    <>
                      <motion.p
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2, duration: 0.4 }}
                        className="mb-8 text-gray-400"
                      >
                        Use the sliders below to reflect your agency's current
                        process.
                        <br />
                        No sales pitch—just honest, practical feedback.
                      </motion.p>
                      <form
                        onSubmit={handleSubmit}
                        className="flex flex-col gap-6"
                        style={{
                          width: "100%",
                          maxWidth: 600,
                          margin: "0 auto",
                        }}
                      >
                          {QUESTIONS.map((q, idx) => (
                            <motion.div
                              key={q.text}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{
                                delay: 0.3 + idx * 0.1,
                                duration: 0.4,
                                ease: [0.4, 0.0, 0.2, 1]
                              }}
                              className="flex flex-col items-stretch gap-2 p-3"
                            >
                              <label className="text-left font-medium text-white mb-0.5 text-base">
                                {q.text}
                              </label>
                              <div className="grid grid-cols-[1fr_auto_1fr] md:grid-cols-[1fr_240px_1fr] items-center w-full">
                                <div className="pr-2 md:pr-4 text-right">
                                  <span className="text-xs md:text-sm text-gray-400">
                                    {q.min}
                                  </span>
                                </div>
                                <motion.div
                                  className="flex justify-center w-[140px] md:w-auto"
                                  whileHover={{ scale: 1.02 }}
                                  transition={{ duration: 0.2 }}
                                >
                                  <input
                                    type="range"
                                    min={0}
                                    max={5}
                                    step="any"
                                    value={responses[idx]}
                                    onChange={(
                                      e: React.ChangeEvent<HTMLInputElement>,
                                    ) => {
                                      // Allow smooth sliding with floating point values
                                      const value = Number.parseFloat(
                                        e.target.value,
                                      );
                                      handleSlider(idx, value);
                                    }}
                                    className="w-full accent-purple-500 slider-thumb h-2 transition-all duration-200"
                                    style={{ accentColor: "#a78bfa" }}
                                  />
                                </motion.div>
                                <div className="pl-2 md:pl-4 text-left">
                                  <span className="text-xs md:text-sm text-gray-400">
                                    {q.max}
                                  </span>
                                </div>
                              </div>
                            </motion.div>
                          ))}
                          <motion.button
                            type="submit"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3 + QUESTIONS.length * 0.1 + 0.2, duration: 0.4 }}
                            whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
                            whileTap={{ scale: 0.98 }}
                            className="btn-primary text-base px-8 py-2 mt-2"
                          >
                            See My Insights
                          </motion.button>
                        </form>
                    </>
                  ) : (
                    <motion.div
                      ref={resultsRef}
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, ease: [0.4, 0.0, 0.2, 1] }}
                    >
                      <motion.div
                        initial={{
                          opacity: 0,
                          scale: 0.95,
                          rotateX: 10,
                        }}
                        animate={{
                          opacity: 1,
                          scale: 1,
                          rotateX: 0,
                        }}
                        transition={{
                          duration: 0.7,
                          ease: [0.4, 0.0, 0.2, 1],
                          delay: 0.2
                        }}
                        className="mt-8 rounded-lg border border-purple-700 bg-purple-900/30 p-8 shadow-lg"
                        style={{ perspective: 1000 }}
                      >
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.4, duration: 0.5 }}
                          className="text-lg font-bold text-purple-300 mb-2"
                        >
                          {result.title}
                        </motion.div>
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.6, duration: 0.5 }}
                          className="text-base text-gray-100 mb-6"
                        >
                          {result.advice}
                        </motion.div>
                        <motion.button
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.8, duration: 0.5 }}
                          whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
                          whileTap={{ scale: 0.95 }}
                          className="btn-secondary px-6 py-2"
                          onClick={handleRetake}
                        >
                          Retake Assessment
                        </motion.button>
                      </motion.div>
                    </motion.div>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>
    </section>
  );
};

export default SelfAssessment;

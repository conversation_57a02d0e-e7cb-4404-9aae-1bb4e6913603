import { animate, svg, utils } from "animejs";
import type React from "react";
import { useCallback, useEffect, useRef, memo } from "react";

// Define the types for props
export type AnimatedLogoTrigger = "mount" | "hover" | "scroll" | "both";
interface AnimatedLogoProps {
  svg: React.FunctionComponent<React.SVGProps<SVGSVGElement>>;
  trigger?: AnimatedLogoTrigger;
  duration?: number;
  delay?: number;
  className?: string;
  style?: React.CSSProperties;
  strokeColor?: string;
}

// The component is memoized to prevent unnecessary re-renders
const AnimatedLogo: React.FC<AnimatedLogoProps> = ({
  svg: SVGComponent,
  trigger = "mount",
  duration = 2000,
  delay = 0,
  className,
  style,
  strokeColor,
}) => {
  // Refs for DOM elements and state management
  const containerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  // Internal ref to track animation state, preventing concurrent animations
  const animationRunning = useRef(false);

  // Helper function to set paths to their final drawn state
  const initializePaths = useCallback(() => {
    try {
      if (!containerRef.current) return;
      const svgEl = containerRef.current.querySelector("svg");
      if (!svgEl) return;
      const nodeList = svgEl.querySelectorAll("path, line, polyline");
      if (!nodeList.length) return;

      // Apply stroke color if provided
      if (strokeColor) {
        for (const el of nodeList) {
          (el as SVGElement).setAttribute("stroke", strokeColor);
        }
      }

      // Set all paths to their fully drawn state instantly
      nodeList.forEach((el) => {
        const d = svg.createDrawable(el);
        animate(d, {
          draw: "1 1", // 100% drawn, no gap
          duration: 0,
          loop: false,
        });
      });
    } catch (e) {
      console.error("Failed to initialize logo paths:", e);
    }
  }, [strokeColor]);

  // Animation state management callbacks
  const animatePathsStarted = useCallback(() => {
    animationRunning.current = true;
  }, []);

  const animatePathsCompleted = useCallback(() => {
    animationRunning.current = false;
  }, []);

  // Core animation logic
  const animatePaths = useCallback(() => {
    // Prevent starting a new animation if one is already running
    if (animationRunning.current) return;

    try {
      if (!containerRef.current) return;
      const svgEl = containerRef.current.querySelector("svg");
      if (!svgEl) return;
      const nodeList = svgEl.querySelectorAll("path, line, polyline");
      if (!nodeList.length) return;

      animatePathsStarted();

      // Apply stroke color if provided
      if (strokeColor) {
        for (const el of nodeList) {
          (el as SVGElement).setAttribute("stroke", strokeColor);
        }
      }

      // Animation sequence timings
      const drawDuration = duration * 0.3;
      const pauseDuration = duration * 0.1;
      const undrawDuration = duration * 0.3;
      const staggerDelay = 100;

      const totalDrawTime = drawDuration + (nodeList.length - 1) * staggerDelay;
      const lastUndrawStartTime =
        delay + totalDrawTime + pauseDuration + (nodeList.length - 1) * staggerDelay;
      const totalAnimationDuration = lastUndrawStartTime + undrawDuration;

      // Animate each path
      for (let idx = 0; idx < nodeList.length; idx++) {
        const el = nodeList[idx];
        const d = svg.createDrawable(el);

        // Phase 1: Draw (from nothing to fully drawn)
        animate(d, {
          draw: ["0 0", "0 1"],
          easing: "easeOutQuad",
          duration: drawDuration,
          delay: delay + idx * staggerDelay,
          loop: false,
        });

        // Phase 2: Undraw (from fully drawn to gone)
        const undrawStartTime =
          delay + totalDrawTime + pauseDuration + idx * staggerDelay;
        setTimeout(() => {
          if (d) {
            animate(d, {
              draw: ["0 1", "1 1"],
              easing: "easeInQuad",
              duration: undrawDuration,
              loop: false,
            });
          }
        }, undrawStartTime);
      }

      // Set a single timeout to mark the animation as complete
      setTimeout(animatePathsCompleted, totalAnimationDuration);
    } catch (e) {
      console.error("Failed to animate logo paths:", e);
      // Ensure the lock is released on error
      animatePathsCompleted();
    }
  }, [
    duration,
    delay,
    strokeColor,
    animatePathsStarted,
    animatePathsCompleted,
  ]);

  // Mount trigger
  useEffect(() => {
    if (trigger === "mount" || trigger === "both") {
      animatePaths();
    } else {
      // For hover/scroll, initialize paths to their final state
      initializePaths();
    }
  }, [animatePaths, initializePaths, trigger]);

  // Hover trigger
  useEffect(() => {
    if (trigger === "hover" || trigger === "both") {
      const handleHover = () => {
        // The check for animationRunning is now inside animatePaths
        animatePaths();
      };

      const node = containerRef.current;
      if (node) {
        node.addEventListener("mouseenter", handleHover);
        return () => node.removeEventListener("mouseenter", handleHover);
      }
    }
  }, [animatePaths, trigger]);

  // Scroll trigger (using Intersection Observer)
  useEffect(() => {
    if (trigger === "scroll" || trigger === "both") {
      const node = containerRef.current;
      if (node) {
        observerRef.current?.disconnect();
        observerRef.current = new IntersectionObserver(
          (entries) => {
            if (entries[0].isIntersecting) {
              animatePaths();
            }
          },
          { threshold: 0.5 },
        );
        observerRef.current.observe(node);
        return () => observerRef.current?.disconnect();
      }
    }
  }, [animatePaths, trigger]);

  // Visibility change handler to fix rendering glitches in background tabs
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible") {
        // Forcefully stop any ongoing or pending animations from anime.js.
        if (containerRef.current) {
          const svgEl = containerRef.current.querySelector("svg");
          if (svgEl) {
            const nodeList = svgEl.querySelectorAll("path, line, polyline");
            utils.remove(nodeList);
          }
        }

        // Force the animation lock to be released.
        animationRunning.current = false;

        // Reset the logo to its fully drawn, non-animated state.
        initializePaths();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [initializePaths]);

  if (!SVGComponent) return null;

  return (
    <div ref={containerRef} className={className} style={style}>
      <SVGComponent
        style={{ display: "block", width: "100%", height: "100%" }}
      />
    </div>
  );
};

export default memo(AnimatedLogo);
